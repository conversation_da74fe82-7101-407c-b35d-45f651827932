/* AddNewStrategy Component Styles */
.add-new-strategy {
  padding: 0;
  background-color: transparent;
  font-family: "Poppins", sans-serif;
}

/* Header Section */
.add-new-strategy__header {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--heading4);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.add-new-strategy__back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--heading6);
}

.add-new-strategy__back-btn:hover {
  background-color: var(--bg-gray);
  border-color: var(--btn-color);
  color: var(--btn-color);
}

.add-new-strategy__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

/* Form Styles */
.add-new-strategy__form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

/* Grid Row for 3-column layout */
.add-new-strategy__grid-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--heading5);
}

/* Field Styles */
.add-new-strategy__field {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.add-new-strategy__label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: var(--smallfont);
}

.add-new-strategy__input {
  padding: 12px var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.add-new-strategy__input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.add-new-strategy__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.add-new-strategy__input.error {
  border-color: #ff3b30;
  box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.1);
}

.add-new-strategy__error {
  color: #ff3b30;
  font-size: var(--extrasmallfont);
  margin-top: 4px;
  display: block;
}

/* Quill Editor Styles */
.add-new-strategy__quill {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.add-new-strategy__quill .ql-toolbar {
  border: 1px solid var(--light-gray);
  border-bottom: none;
  background-color: var(--bg-gray);
}

.add-new-strategy__quill .ql-container {
  border: 1px solid var(--light-gray);
  font-family: "Poppins", sans-serif;
  font-size: var(--basefont);
  min-height: 120px;
}

.add-new-strategy__quill .ql-editor {
  min-height: 120px;
  padding: var(--basefont);
}

.add-new-strategy__quill .ql-editor.ql-blank::before {
  color: var(--dark-gray);
  opacity: 0.7;
  font-style: normal;
}

.add-new-strategy__quill.error .ql-container {
  border-color: #ff3b30;
}

.add-new-strategy__quill.error .ql-toolbar {
  border-color: #ff3b30;
}

/* Upload Box Styles */
.add-new-strategy__upload-box {
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading4);
  text-align: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-new-strategy__upload-box:hover {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.02);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.add-new-strategy__upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
}

.add-new-strategy__upload-icon {
  font-size: var(--heading1);
  color: var(--btn-color);
  margin-bottom: var(--basefont);
  opacity: 0.7;
}

.add-new-strategy__upload-text {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 var(--smallfont) 0;
}

.add-new-strategy__upload-subtext {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
  opacity: 0.8;
}

/* Action Buttons */
.add-new-strategy__actions {
  display: flex;
  gap: var(--basefont);
  justify-content: center;
  margin-top: var(--heading5);
  padding-top: var(--heading5);
  border-top: 1px solid var(--light-gray);
}

.add-new-strategy__submit-btn,
.add-new-strategy__clear-btn {
  padding: 12px var(--heading4);
  font-size: var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
}

.add-new-strategy__submit-btn:disabled,
.add-new-strategy__clear-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.add-new-strategy__submit-btn:disabled:hover,
.add-new-strategy__clear-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .add-new-strategy__grid-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .add-new-strategy__actions {
    flex-direction: column;
    align-items: center;
  }

  .add-new-strategy__submit-btn,
  .add-new-strategy__clear-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .add-new-strategy {
    padding: 0;
  }

  .add-new-strategy__header {
    margin-bottom: var(--heading5);
    padding-bottom: var(--smallfont);
  }

  .add-new-strategy__title {
    font-size: var(--heading5);
  }

  .add-new-strategy__grid-row {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .add-new-strategy__form {
    gap: var(--basefont);
  }

  .add-new-strategy__upload-box {
    padding: var(--heading5);
  }

  .add-new-strategy__upload-icon {
    font-size: var(--heading3);
  }

  .add-new-strategy__quill .ql-editor {
    min-height: 100px;
  }
}

@media (max-width: 480px) {
  .add-new-strategy__header {
    gap: var(--smallfont);
  }

  .add-new-strategy__back-btn {
    width: 36px;
    height: 36px;
    font-size: var(--basefont);
  }

  .add-new-strategy__title {
    font-size: var(--heading6);
  }

  .add-new-strategy__input {
    padding: 10px var(--smallfont);
  }

  .add-new-strategy__upload-box {
    padding: var(--basefont);
  }

  .add-new-strategy__actions {
    gap: var(--smallfont);
  }

  .add-new-strategy__submit-btn,
  .add-new-strategy__clear-btn {
    padding: 10px var(--basefont);
    font-size: var(--smallfont);
  }
}
