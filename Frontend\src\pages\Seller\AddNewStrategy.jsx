import { useState } from "react";
import { useNavigate } from "react-router-dom";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { MdArrowBack, MdCloudUpload } from "react-icons/md";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/AddNewStrategy.css";

const AddNewStrategy = () => {
  const navigate = useNavigate();

  // Form state
  const [formData, setFormData] = useState({
    title: "",
    sport: "",
    strategyTags: "",
    description: "",
    aboutCoach: "",
    strategicContent: "",
  });

  // Form validation state
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.sport) {
      newErrors.sport = "Sport selection is required";
    }

    if (!formData.description.trim() || formData.description === '<p><br></p>') {
      newErrors.description = "Description is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  // Handle Quill editor changes
  const handleQuillChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement actual API call
      console.log("Form submitted:", formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Navigate back to strategies page on success
      navigate("/seller/my-sports-strategies");
    } catch (error) {
      console.error("Error submitting form:", error);
      // Handle error (could show toast notification)
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle clear form
  const handleClearForm = () => {
    setFormData({
      title: "",
      sport: "",
      strategyTags: "",
      description: "",
      aboutCoach: "",
      strategicContent: "",
    });
  };

  // Handle back navigation
  const handleBack = () => {
    navigate("/seller/my-sports-strategies");
  };

  // Quill modules configuration
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ],
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'link'
  ];

  return (
    <SellerLayout>
      <div className="add-new-strategy">
        {/* Header Section */}
        <div className="add-new-strategy__header">
          <button
            className="add-new-strategy__back-btn"
            onClick={handleBack}
            type="button"
          >
            <MdArrowBack />
          </button>
          <h1 className="add-new-strategy__title">Add New Strategy</h1>
        </div>

        {/* Form Section */}
        <form className="add-new-strategy__form" onSubmit={handleSubmit}>
          {/* Top Row - 3 Column Grid */}
          <div className="add-new-strategy__grid-row">
            <div className="add-new-strategy__field">
              <label className="add-new-strategy__label">Title</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`add-new-strategy__input ${errors.title ? 'error' : ''}`}
                placeholder="Enter strategy title"
                required
              />
              {errors.title && <span className="add-new-strategy__error">{errors.title}</span>}
            </div>

            <div className="add-new-strategy__field">
              <label className="add-new-strategy__label">Sport</label>
              <select
                name="sport"
                value={formData.sport}
                onChange={handleInputChange}
                className={`add-new-strategy__input ${errors.sport ? 'error' : ''}`}
                required
              >
                <option value="">Select Sport</option>
                <option value="basketball">Basketball</option>
                <option value="football">Football</option>
                <option value="soccer">Soccer</option>
                <option value="baseball">Baseball</option>
                <option value="tennis">Tennis</option>
                <option value="volleyball">Volleyball</option>
                <option value="other">Other</option>
              </select>
              {errors.sport && <span className="add-new-strategy__error">{errors.sport}</span>}
            </div>

            <div className="add-new-strategy__field">
              <label className="add-new-strategy__label">Strategy Tags</label>
              <input
                type="text"
                name="strategyTags"
                value={formData.strategyTags}
                onChange={handleInputChange}
                className="add-new-strategy__input"
                placeholder="e.g., offense, defense, training"
              />
            </div>
          </div>

          {/* Description Section */}
          <div className="add-new-strategy__field">
            <label className="add-new-strategy__label">Description for Strategy</label>
            <ReactQuill
              theme="snow"
              value={formData.description}
              onChange={(value) => handleQuillChange('description', value)}
              modules={quillModules}
              formats={quillFormats}
              className={`add-new-strategy__quill ${errors.description ? 'error' : ''}`}
              placeholder="Describe your strategy in detail..."
            />
            {errors.description && <span className="add-new-strategy__error">{errors.description}</span>}
          </div>

          {/* Upload Section */}
          <div className="add-new-strategy__field">
            <label className="add-new-strategy__label">Upload Video/Document</label>
            <div className="add-new-strategy__upload-box">
              <div className="add-new-strategy__upload-content">
                <MdCloudUpload className="add-new-strategy__upload-icon" />
                <p className="add-new-strategy__upload-text">
                  Drag and drop files here or click to browse
                </p>
                <p className="add-new-strategy__upload-subtext">
                  Supported formats: MP4, PDF, DOC, DOCX (Max 100MB)
                </p>
              </div>
            </div>
          </div>

          {/* About Coach Section */}
          <div className="add-new-strategy__field">
            <label className="add-new-strategy__label">About the Coach</label>
            <ReactQuill
              theme="snow"
              value={formData.aboutCoach}
              onChange={(value) => handleQuillChange('aboutCoach', value)}
              modules={quillModules}
              formats={quillFormats}
              className="add-new-strategy__quill"
              placeholder="Tell us about yourself and your coaching experience..."
            />
          </div>

          {/* Strategic Content Section */}
          <div className="add-new-strategy__field">
            <label className="add-new-strategy__label">Include Strategic Content</label>
            <ReactQuill
              theme="snow"
              value={formData.strategicContent}
              onChange={(value) => handleQuillChange('strategicContent', value)}
              modules={quillModules}
              formats={quillFormats}
              className="add-new-strategy__quill"
              placeholder="Include detailed strategic content, plays, techniques..."
            />
          </div>

          {/* Action Buttons */}
          <div className="add-new-strategy__actions">
            <button
              type="submit"
              disabled={isSubmitting}
              className="add-new-strategy__submit-btn btn signupbtn"
            >
              {isSubmitting ? "Adding Strategy..." : "Add New Strategy"}
            </button>
            <button
              type="button"
              onClick={handleClearForm}
              disabled={isSubmitting}
              className="add-new-strategy__clear-btn btn signinbtn"
            >
              Clear Form
            </button>
          </div>
        </form>
      </div>
    </SellerLayout>
  );
};

export default AddNewStrategy;
